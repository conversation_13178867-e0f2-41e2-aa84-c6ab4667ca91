#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import functools
import inspect
import logging
import os
import tempfile
from typing import Any, Callable, Optional, Type, Union

from decursion.decursion import detect_recursion


def _get_fully_qualified_name(obj: Any) -> str:
    """Get the fully qualified name of an object."""
    module = obj.__module__
    if hasattr(obj, '__qualname__'):
        qualname = obj.__qualname__
    elif hasattr(obj, '__class__'):  # For a special case that a class instance self is callable.
        qualname = obj.__class__.__name__
        module = obj.__class__.__module__
    else:
        raise AttributeError("{0} does not have both of  __qualname__ and __class__ attribute".format(obj))
    return f"{module}.{qualname}"


def get_fully_qualified_name_by_callable(callee: Union[Callable, property]) -> str:
    """Get fully qualified name for callable objects including properties and classmethods."""
    if not callable(callee) and not isinstance(callee, property) and not isinstance(callee, classmethod):
        raise ValueError("Expected callable or property as value of callee argument, but actual {0}".format(callee))

    if isinstance(callee, property):
        callee = callee.fget  # Use the getter function for properties
    elif isinstance(callee, classmethod):
        callee = callee.__func__  # Use the underlying function for classmethods

    return _get_fully_qualified_name(obj=callee)


def gen_logger_name_by_callable(callee: Callable) -> str:
    """Generate logger name based on callable object."""
    if not callable(callee) and not isinstance(callee, classmethod):
        raise ValueError("Expected callable object as value of {0} argument, but actual {1}".format('callee', callee))

    # Handle classmethod objects specially
    if isinstance(callee, classmethod):
        underlying_func = callee.__func__
        return get_fully_qualified_name_by_callable(callee=underlying_func).rsplit(sep='.', maxsplit=1)[0]

    self_obj = getattr(callee, '__self__', None)
    if self_obj is None:
        return get_fully_qualified_name_by_callable(callee=callee).rsplit(sep='.', maxsplit=1)[0]

    obj_hash = hex(hash(self_obj))

    # Handle the case where self_obj is a class (for classmethods)
    if inspect.isclass(self_obj):
        # For classmethods, self_obj is the class itself
        logger_name = "{0}.{1}".format(self_obj.__module__, self_obj.__qualname__)
    else:
        # For instance methods, self_obj is an instance
        logger_name = "{0}.{1}_{2}".format(self_obj.__module__, type(self_obj).__qualname__, obj_hash)

    return logger_name


def add_log_handlers(logger: logging.Logger):
    """Add file and stream handlers to logger if they don't exist."""
    has_file_handler = False
    has_stream_handler = False
    for handler in logger.handlers:
        if not has_file_handler:
            has_file_handler = isinstance(handler, logging.FileHandler)
        if not has_stream_handler:
            has_stream_handler = isinstance(handler, logging.StreamHandler)

    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    if not has_file_handler:
        logfd, logfile = tempfile.mkstemp(
            prefix="{0}.".format(logger.name), suffix="{0}log".format(os.path.extsep), text=True)
        fh = logging.FileHandler(logfile)
        fh.setFormatter(formatter)
        logger.addHandler(fh)

    if not has_stream_handler:
        ch = logging.StreamHandler()
        ch.setFormatter(formatter)
        logger.addHandler(ch)


def get_logger_by_callable(callee: Callable) -> logging.Logger:
    """Get logger instance for a callable object."""
    logger_name = gen_logger_name_by_callable(callee=callee)
    logger_obj = logging.getLogger(logger_name)
    add_log_handlers(logger_obj)
    return logger_obj


class AutoExceptionLogging:
    """
    A cleaner implementation of auto exception logging decorator in descriptor design.
    
    This class implements the descriptor protocol to properly handle different types of decorators
    (functions, methods, classmethods, staticmethods, properties) in a unified and maintainable way.
    """
    
    def __init__(self, wrapped_item: Union[Callable, property], logger: Optional[logging.Logger] = None):
        self.wrapped_item = wrapped_item
        self.logger = logger
        self._update_wrapper_for_init()

    def _update_wrapper_for_init(self):
        """Update wrapper attributes to preserve metadata from the wrapped item."""
        try:
            functools.update_wrapper(self, self.wrapped_item)
        except AttributeError:
            target_for_update = self.wrapped_item
            if isinstance(target_for_update, (classmethod, staticmethod)):
                target_for_update = target_for_update.__func__
            if isinstance(target_for_update, property) and target_for_update.fget:
                target_for_update = target_for_update.fget

            functools.update_wrapper(self, target_for_update)

        # Preserve the signature
        self._preserve_signature()

    def _preserve_signature(self):
        """Preserve the signature of the wrapped item."""
        try:
            if isinstance(self.wrapped_item, (classmethod, staticmethod)):
                # For classmethod/staticmethod, get signature from the underlying function
                self.__signature__ = inspect.signature(self.wrapped_item.__func__)
            elif isinstance(self.wrapped_item, property) and self.wrapped_item.fget:
                # For property, get signature from the getter function
                self.__signature__ = inspect.signature(self.wrapped_item.fget)
            else:
                # For regular functions
                self.__signature__ = inspect.signature(self.wrapped_item)
        except (ValueError, TypeError):
            # If signature can't be extracted, don't set it
            pass

    def _is_fundamentally_property(self) -> bool:
        """Check if the wrapped item is fundamentally a property."""
        item = self.wrapped_item
        if isinstance(item, property): 
            return True
        if isinstance(item, (classmethod, staticmethod)) and isinstance(item.__func__, property): 
            return True
        return False

    def _get_core_property_and_outer_wrapper(self):
        """
        Extract core property and outer wrapper information.
        Returns: (core_property_or_None, outer_wrapper_type_or_None, original_callable_if_no_property_or_None)
        """
        if isinstance(self.wrapped_item, property):
            return self.wrapped_item, None, None
        elif isinstance(self.wrapped_item, (classmethod, staticmethod)):
            inner_func = self.wrapped_item.__func__
            if isinstance(inner_func, property):
                return inner_func, type(self.wrapped_item), None
            else:  # e.g., @AutoExceptionLogging @classmethod def foo(): ...
                return None, type(self.wrapped_item), inner_func
        elif callable(self.wrapped_item):  # A simple function
            return None, None, self.wrapped_item
        else:
            # This case should ideally not be hit if used as a decorator on common class elements
            raise TypeError(f"Wrapped item is {type(self.wrapped_item).__name__}, not a recognized type for property operations.")

    def _create_exception_logging_wrapper(self, next_executable: Callable, instance=None) -> Callable:
        """Create the actual exception logging wrapper function to use descriptor protocol implementation."""
        @detect_recursion()
        @functools.wraps(next_executable)
        def exception_logging_wrapper(*args, **kwargs):
            try:
                # --- Pre-call logic: exception logging setup ---
                result = next_executable(*args, **kwargs)
                # --- Post-call logic: could add success logging here if needed ---
                return result
            except Exception as e:
                # Pass the next_executable to _log_exception for better context
                self._log_exception(e, args, kwargs, instance, next_executable)
                raise

        # Preserve signature if possible
        try:
            if hasattr(next_executable, '__signature__'):
                exception_logging_wrapper.__signature__ = next_executable.__signature__
            else:
                exception_logging_wrapper.__signature__ = inspect.signature(next_executable)
        except (ValueError, TypeError):
            pass  # If signature can't be copied, continue without it

        return exception_logging_wrapper

    def _log_exception(self, exception: Exception, args: tuple, kwargs: dict, instance=None, next_executable=None):
        """Log the exception using appropriate logger."""
        if self.logger:
            self.logger.exception(exception, stack_info=True)
        else:
            # Determine appropriate logger based on the callable type and arguments
            logger_obj = self._get_appropriate_logger(args, instance, next_executable)
            logger_obj.exception(exception, stack_info=True)

    def _get_appropriate_logger(self, args: tuple, instance=None, next_executable=None) -> logging.Logger:
        """Get the appropriate logger for the given callable and arguments."""
        if isinstance(self.wrapped_item, property):
            return self._get_logger_for_property(self.wrapped_item, args[0])
        elif isinstance(self.wrapped_item, classmethod):
            # For classmethod, we need to create a bound method to get proper logger name
            # args[0] is the class when classmethod is called
            if len(args) > 0:
                cls = args[0]
                # Create a bound method from the classmethod
                bound_method = self.wrapped_item.__get__(None, cls)
                return get_logger_by_callable(callee=bound_method)

            # Fallback to using the underlying function
            underlying_func = self.wrapped_item.__func__
            return get_logger_by_callable(callee=underlying_func)
        elif isinstance(self.wrapped_item, staticmethod):
            # For staticmethod, use the underlying function
            underlying_func = self.wrapped_item.__func__
            return get_logger_by_callable(callee=underlying_func)
        else:
            # For regular methods/functions, use a safer approach that avoids getattr recursion

            # First, check if we have a bound method from next_executable (for classmethods with empty args)
            if next_executable and hasattr(next_executable, '__self__') and inspect.isclass(next_executable.__self__):
                # This is a bound classmethod - use the class for logger name
                cls = next_executable.__self__
                logger_name = f"{cls.__module__}.{cls.__qualname__}"
                logger_obj = logging.getLogger(logger_name)
                add_log_handlers(logger_obj)
                return logger_obj

            # Second, check if we have an instance passed from the wrapper
            if instance is not None:
                obj_hash = hex(hash(instance))
                logger_name = f"{instance.__module__}.{type(instance).__qualname__}_{obj_hash}"
                logger_obj = logging.getLogger(logger_name)
                add_log_handlers(logger_obj)
                return logger_obj

            # Third, check if we have an instance in args[0] (for unbound method calls)
            if len(args) > 0:
                obj = args[0]
                # Check if args[0] is a class (for classmethods that weren't caught above)
                if inspect.isclass(obj):
                    # For classmethods, generate class-based logger name
                    logger_name = f"{obj.__module__}.{obj.__qualname__}"
                    logger_obj = logging.getLogger(logger_name)
                    add_log_handlers(logger_obj)
                    return logger_obj
                elif hasattr(obj, '__class__'):
                    # For instance methods, generate instance-based logger name
                    obj_hash = hex(hash(obj))
                    logger_name = f"{obj.__module__}.{type(obj).__qualname__}_{obj_hash}"
                    logger_obj = logging.getLogger(logger_name)
                    add_log_handlers(logger_obj)
                    return logger_obj

            # Fallback to using the wrapped item directly (unbound method/function)
            return get_logger_by_callable(callee=self.wrapped_item)

    def _get_logger_for_property(self, prop: property, owner_instance: object) -> logging.Logger:
        """Get logger for property access."""
        if not isinstance(prop, property):
            raise ValueError(
                "Expected property object as value of {0} argument, but actual {1}".format('prop', prop))

        prop_mbrs = dict(filter(lambda e: isinstance(e[1], property), inspect.getmembers(type(owner_instance))))

        if not (inspect.unwrap(prop.fget) in list(map(lambda p: inspect.unwrap(p.fget), prop_mbrs.values()))):
            raise ValueError(
                "Expected {0} (value of {1} argument) held in {2} (value of {3} argument) but actual not.".format(
                    prop, 'prop', owner_instance, 'owner_instance'))

        obj_hash = hex(hash(owner_instance))
        logger_name = "{0}.{1}_{2}".format(owner_instance.__module__, type(owner_instance).__qualname__, obj_hash)
        logger_obj = logging.getLogger(logger_name)
        add_log_handlers(logger_obj)
        return logger_obj

    def __get__(self, instance, owner):
        """Descriptor protocol pattern implementation."""
        # Initial check for non-descriptors or items that don't have __get__
        if not hasattr(self.wrapped_item, '__get__'):
            if instance is not None and callable(self.wrapped_item):
                # Avoid re-binding if already bound (e.g. functools.partial or method already bound)
                is_already_bound = (
                    isinstance(self.wrapped_item, functools.partial) or
                    (hasattr(self.wrapped_item, '__self__') and self.wrapped_item.__self__ is not None)
                )
                if not is_already_bound:
                    return self._create_exception_logging_wrapper(functools.partial(self.wrapped_item, instance))
            return self._create_exception_logging_wrapper(self.wrapped_item)

        if self._is_fundamentally_property():
            # Special handling for @AutoExceptionLogging @staticmethod @property
            if isinstance(self.wrapped_item, staticmethod) and isinstance(self.wrapped_item.__func__, property):
                actual_property_object = self.wrapped_item.__func__
                if actual_property_object.fget:
                    # The fget of this property is the original static function.
                    # Call it directly to get the value with exception logging.
                    try:
                        return actual_property_object.fget()
                    except Exception as e:
                        self._log_exception(e, (), {})
                        raise
                else:
                    # No getter, standard behavior is to raise AttributeError or return property
                    raise AttributeError("unreadable static property")

            # For @AutoExceptionLogging @property or @AutoExceptionLogging @classmethod @property:
            # self.wrapped_item.__get__ already returns the final value.
            try:
                return self.wrapped_item.__get__(instance, owner)
            except Exception as e:
                # For properties, we need to log exceptions during property access
                self._log_exception(e, (instance,) if instance else (), {})
                raise
        else:
            # It's a method (regular, class, or static but not wrapping a property)
            # Get resolved item and wrap it
            resolved_item = self.wrapped_item.__get__(instance, owner)
            next_executable = resolved_item
            wrapper = self._create_exception_logging_wrapper(next_executable, instance)

            # Set __self__ attribute on the wrapper if we have an instance
            # This ensures get_logger_by_callable treats it as a bound method
            if instance is not None and hasattr(resolved_item, '__self__'):
                wrapper.__self__ = resolved_item.__self__

            return wrapper



    def __set__(self, instance, value):
        """Descriptor protocol implementation for property setters."""
        core_property, _, _ = self._get_core_property_and_outer_wrapper()
        if core_property and hasattr(core_property, '__set__'):
            try:
                core_property.__set__(instance, value)
                return
            except Exception as e:
                # Log exceptions during property setting
                self._log_exception(e, (instance, value), {})
                raise
        # Fallback for other data descriptors this decorator might wrap directly
        elif hasattr(self.wrapped_item, '__set__') and isinstance(self.wrapped_item, property):
            try:
                self.wrapped_item.__set__(instance, value)
                return
            except Exception as e:
                # Log exceptions during property setting
                self._log_exception(e, (instance, value), {})
                raise
        name = "attribute"
        try:
            if core_property and core_property.fget:
                name = core_property.fget.__name__
        except:
            pass
        raise AttributeError(f"can't set attribute '{name}', not a settable property or descriptor")

    def __delete__(self, instance):
        """Descriptor protocol implementation for property deleters."""
        core_property, _, _ = self._get_core_property_and_outer_wrapper()
        if core_property and hasattr(core_property, '__delete__'):
            core_property.__delete__(instance)
            return
        elif hasattr(self.wrapped_item, '__delete__') and isinstance(self.wrapped_item, property):
            self.wrapped_item.__delete__(instance)
            return
        name = "attribute"
        try:
            if core_property and core_property.fget:
                name = core_property.fget.__name__
        except:
            pass
        raise AttributeError(f"can't delete attribute '{name}', not a deletable property or descriptor")

    def __call__(self, *args, **kwargs):
        """Make AutoExceptionLogging callable for direct invocation by descriptor protocol pattern."""
        # For direct calls, we need to handle the wrapped item appropriately
        if isinstance(self.wrapped_item, staticmethod):
            # For staticmethod, call the underlying function directly
            underlying_func = self.wrapped_item.__func__
            try:
                return underlying_func(*args, **kwargs)
            except Exception as e:
                self._log_exception(e, args, kwargs)
                raise
        elif isinstance(self.wrapped_item, classmethod):
            # For classmethod, this shouldn't happen in normal usage, but handle it
            raise TypeError("classmethod object is not directly callable - use descriptor protocol")
        elif callable(self.wrapped_item):
            # For regular functions
            try:
                return self.wrapped_item(*args, **kwargs)
            except Exception as e:
                self._log_exception(e, args, kwargs)
                raise
        else:
            raise TypeError(f"'{type(self.wrapped_item).__name__}' object is not callable")

    @classmethod
    def get_wrapped_callable(cls, callable_obj: Callable, *, allow_descriptor_lookup: bool = True) -> Callable:
        """
        Get the original wrapped callable from a decorated callable.

        This method peels off one layer of wrapping at a time to handle different decorator orders:
        - @classmethod @auto_exception_logging(): returns AutoExceptionLogging, then function
        - @auto_exception_logging() @classmethod: returns classmethod, then function

        Args:
            callable_obj: A callable that may be wrapped by AutoExceptionLogging or other decorators
            allow_descriptor_lookup: If True (default), allows looking up descriptors from class __dict__
                                   when given a bound method or function with qualified name.
                                   If False, only unwraps direct decorator/descriptor objects.

        Returns:
            The next layer of wrapping, or the input callable if not wrapped

        Note:
            When allow_descriptor_lookup=True, calling this method repeatedly on the same
            function may create a loop (function -> descriptor -> function -> descriptor...).
            Set allow_descriptor_lookup=False to prevent this behavior and only unwrap
            direct decorator objects.
        """
                
        current = callable_obj

        # Handle AutoExceptionLogging descriptor instances
        if isinstance(current, cls):
            return current.wrapped_item

        # Handle direct classmethod, staticmethod, and property objects
        if isinstance(current, classmethod):
            # If the classmethod wraps an AutoExceptionLogging, return the AutoExceptionLogging
            if isinstance(current.__func__, cls):
                return current.__func__
            # Otherwise return the underlying function
            return current.__func__
        elif isinstance(current, staticmethod):
            # If the staticmethod wraps an AutoExceptionLogging, return the AutoExceptionLogging
            if isinstance(current.__func__, cls):
                return current.__func__
            return current.__func__
        elif isinstance(current, property):
            # If the property wraps an AutoExceptionLogging, return the AutoExceptionLogging
            if isinstance(current.fget, cls):
                return current.fget
            return current.fget

        # Handle cases where we need to access the descriptor from class __dict__
        if allow_descriptor_lookup and hasattr(current, '__qualname__'):
            qualname_parts = current.__qualname__.split('.')
            if len(qualname_parts) >= 2:
                method_name = qualname_parts[-1]  # Method name

                # For bound methods, we can get the class directly from __self__
                if hasattr(current, '__self__'):
                    if inspect.isclass(current.__self__):
                        # This is a bound classmethod
                        target_class = current.__self__
                    else:
                        # This is a bound instance method
                        target_class = current.__self__.__class__

                    if method_name in target_class.__dict__:
                        descriptor = target_class.__dict__[method_name]
                        if isinstance(descriptor, cls):
                            return descriptor.wrapped_item
                        elif isinstance(descriptor, (classmethod, staticmethod, property)):
                            # Handle direct descriptor objects that aren't wrapped by AutoExceptionLogging
                            return descriptor
                        elif callable(descriptor):
                            # Handle plain functions (instance methods)
                            return descriptor

                # Fallback: Try to find the original descriptor in the class hierarchy via module navigation
                # This handles cases where we don't have __self__ (like staticmethods and unbound functions)
                func_module = inspect.getmodule(current)
                if func_module:
                    class_path = qualname_parts[:-1]  # All parts except method name

                    # Check if this is a locally defined class (contains <locals>)
                    if '<locals>' in class_path:
                        # For locally defined classes, we can't navigate via module attributes
                        # Try to find the class by searching through the call stack frames
                        target_class = cls._find_local_class_in_frames(qualname_parts)
                        if target_class and method_name in target_class.__dict__:
                            descriptor = target_class.__dict__[method_name]
                            if isinstance(descriptor, cls):
                                return descriptor.wrapped_item
                            elif isinstance(descriptor, (classmethod, staticmethod, property)):
                                return descriptor
                            elif callable(descriptor):
                                return descriptor
                    else:
                        # For module-level classes, use normal navigation
                        current_obj = func_module
                        for part in class_path:
                            if hasattr(current_obj, part):
                                current_obj = getattr(current_obj, part)
                            else:
                                break

                        if inspect.isclass(current_obj) and method_name in current_obj.__dict__:
                            descriptor = current_obj.__dict__[method_name]
                            if isinstance(descriptor, cls):
                                return descriptor.wrapped_item
                            elif isinstance(descriptor, (classmethod, staticmethod, property)):
                                return descriptor
                            elif callable(descriptor):
                                return descriptor

        # Handle other decorators with __wrapped__
        while hasattr(current, '__wrapped__'):
            current = current.__wrapped__

        return current

    @classmethod
    def _find_local_class_in_frames(cls, qualname_parts: list) -> type:
        """
        Find a locally defined class by searching through call stack frames.

        Args:
            qualname_parts: List of qualname parts like ['func_name', '<locals>', 'ClassName', 'method_name']

        Returns:
            The class object if found, None otherwise
        """
        import sys

        # Extract the class name (should be the part after <locals>)
        try:
            locals_index = qualname_parts.index('<locals>')
            if locals_index + 1 < len(qualname_parts):
                class_name = qualname_parts[locals_index + 1]
            else:
                return None
        except ValueError:
            return None

        # Search through the call stack frames
        frame = sys._getframe()
        while frame:
            # Check if the class is in the frame's locals
            if class_name in frame.f_locals:
                potential_class = frame.f_locals[class_name]
                if inspect.isclass(potential_class):
                    return potential_class

            # Check if the class is in the frame's globals
            if class_name in frame.f_globals:
                potential_class = frame.f_globals[class_name]
                if inspect.isclass(potential_class):
                    return potential_class

            frame = frame.f_back

        return None


def auto_exception_logging(logger: Optional[logging.Logger] = None) -> Callable[[Union[Callable, property]], Union[Callable, property]]:
    """
    Cleaner implementation of auto exception logging decorator.

    This function creates AutoExceptionLogging instances that properly handle
    different types of decorators using the descriptor protocol.
    """
    def autolog_exception(callee: Union[Callable, property]) -> Union[AutoExceptionLogging, property]:
        if type(callee) == type or (not callable(callee)
                                    and not isinstance(callee, classmethod)
                                    and not isinstance(callee, staticmethod)
                                    and not isinstance(callee, property)):
            raise ValueError("Expected either of function/method/property as {0} argument value, but actual {1}".format(
                'callee', callee))

        # Use AutoExceptionLogging descriptor for all cases by using descriptor protocol pattern.
        return AutoExceptionLogging(wrapped_item=callee, logger=logger)

    return autolog_exception


def default_shall_wrap(clz: Type, callee_name: str) -> bool:
    """Default function to determine if a callable should be wrapped with auto exception logging."""
    # Skip private/protected methods
    if callee_name.startswith('_') or callee_name == "grab_logger":
        return False

    # Skip async methods as they cannot be properly handled by auto_exception_logging
    # TODO: Need to make sure callable obtained via getattr preserve coroutine annotation
    callable_obj = getattr(clz, callee_name, None)
    if callable_obj and inspect.iscoroutinefunction(callable_obj):
        return False

    return True


def adapt_autolog_exception(shall_wrap: Callable[[Type, str], bool] = default_shall_wrap,
                            logger: Optional[logging.Logger] = None):
    """
    Class decorator that applies auto exception logging to all appropriate methods in a class.

    This is a cleaner implementation that uses the new AutoExceptionLogging class.
    """
    # Find the true owner class of an attribute
    def find_attribute_owner(attr_name: str, cls: Type) -> Union[Type, None]:
        for base in cls.mro():
            if attr_name in base.__dict__:
                return base
        return None

    def autolog_exception_for_class(cls: type) -> type:
        mbs = dict(inspect.getmembers(cls))
        # screen to only callable and property
        callable_mbs = dict(filter(lambda t:
                                   ((callable(t[1]) and type(t[1]) != type) or isinstance(t[1], property))
                                   and not inspect.isbuiltin(t[1]),
                                   mbs.items()))

        sample_callable_name = None
        sample_callable_obj = None
        wrapped_callable_names = set()  # Track which callables were actually wrapped
        for name, callable_obj in callable_mbs.items():
            if not shall_wrap(cls, name):
                continue

            wrapped_callable_names.add(name)  # Add to wrapped set
            sample_callable_name = name
            sample_callable_obj = callable_obj
            autolog_exception_func = auto_exception_logging(logger=logger)

            params = list(inspect.signature(callable_obj).parameters.values()) \
                if not isinstance(callable_obj, property) else None
            if params is None or (len(params) > 0 and params[0].name in ['self', 'cls']):
                wrapped = autolog_exception_func(callee=callable_obj)
            else:   # staticmethod case
                # Find the class where the method is originally defined
                owner_class = find_attribute_owner(attr_name=name, cls=cls)
                if owner_class is None:
                    raise RuntimeError("Failed to find {0} attribute even through MRO of {1} class.".format(name, cls))

                wrapped = autolog_exception_func(callee=owner_class.__dict__.get(name))

            setattr(cls, name, wrapped)

        if sample_callable_name:
            def grab_logger(*args) -> logging.Logger:
                if logger:
                    return logger

                if len(args) > 0 and isinstance(args[0], cls):
                    prop_name = None
                    callable_name = None
                    # Only consider wrapped callables to avoid recursion
                    for name in wrapped_callable_names:
                        for clz in type(args[0]).__mro__:
                            if name in clz.__dict__:
                                descriptor = clz.__dict__[name]
                                # Check if it's a property or AutoExceptionLogging wrapping a property
                                is_property = (isinstance(descriptor, property) or
                                             (isinstance(descriptor, AutoExceptionLogging) and
                                              descriptor._is_fundamentally_property()))
                                if is_property and prop_name is None:
                                    prop_name = name
                                else:
                                    callable_name = name
                                    break

                    if callable_name:
                        return get_logger_by_callable(callee=getattr(args[0], callable_name))
                    elif prop_name:
                        logger_name = _get_fully_qualified_name(obj=args[0])
                        logger_obj = logging.getLogger(logger_name)
                        add_log_handlers(logger_obj)
                        return logger_obj
                    else:   # Should not reach here.
                        raise RuntimeError(
                            "Failed to find any of (not-builtin) callable or property in {0} class.".format(cls))

                else:
                    return get_logger_by_callable(callee=sample_callable_obj)

            if not callable_mbs.get(grab_logger.__name__):
                setattr(cls, grab_logger.__name__, grab_logger)

        return cls

    return autolog_exception_for_class
